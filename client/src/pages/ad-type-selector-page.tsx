"use client";
import { useState, useEffect, useRef } from "react";
import { ArrowRight, Link, Zap, Facebook, Instagram, Youtube, Linkedin, Globe, Monitor, X, ArrowLeft, Sparkles } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useLocation } from "wouter";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { useToast } from "@/hooks/use-toast";

interface AdTypeItem {
  id: number;
  title: string;
  date: string;
  content: string;
  category: string;
  icon: React.ElementType;
  relatedIds: number[];
  status: "completed" | "in-progress" | "pending";
  energy: number;
  platform: string;
  sizes: string[];
  description: string;
  color: string;
}

interface RadialOrbitalTimelineProps {
  timelineData: AdTypeItem[];
}

const adTypesData: AdTypeItem[] = [
  {
    id: 1,
    title: "Facebook Ads",
    date: "Formato cuadrado",
    content: "Anuncios optimizados para Facebook con formato cuadrado 1:1. Perfectos para feed y stories.",
    category: "Social Media",
    icon: Facebook,
    relatedIds: [2, 3],
    status: "completed",
    energy: 95,
    platform: "facebook",
    sizes: ["1080x1080", "1200x1200"],
    description: "Crea anuncios impactantes para Facebook",
    color: "from-[#3018ef] to-[#dd3a5a]"
  },
  {
    id: 2,
    title: "Instagram Posts",
    date: "Formato cuadrado",
    content: "Anuncios para Instagram feed con formato cuadrado. Diseño optimizado para engagement.",
    category: "Social Media",
    icon: Instagram,
    relatedIds: [1, 4],
    status: "completed",
    energy: 90,
    platform: "instagram",
    sizes: ["1080x1080", "1200x1200"],
    description: "Diseños perfectos para Instagram",
    color: "from-[#dd3a5a] to-[#3018ef]"
  },
  {
    id: 3,
    title: "Instagram Stories",
    date: "Formato vertical",
    content: "Anuncios verticales para Instagram Stories. Formato inmersivo 9:16.",
    category: "Social Media",
    icon: Instagram,
    relatedIds: [2, 5],
    status: "completed",
    energy: 85,
    platform: "instagram-stories",
    sizes: ["1080x1920", "1200x2133"],
    description: "Stories que capturan la atención",
    color: "from-[#3018ef] to-[#dd3a5a]"
  },
  {
    id: 4,
    title: "Google Ads",
    date: "Formato horizontal",
    content: "Banners para Google Ads con formato horizontal. Optimizados para conversión.",
    category: "Search Ads",
    icon: Globe,
    relatedIds: [6, 7],
    status: "completed",
    energy: 88,
    platform: "google",
    sizes: ["1200x628", "1536x1024"],
    description: "Banners que convierten en Google",
    color: "from-[#dd3a5a] to-[#3018ef]"
  },
  {
    id: 5,
    title: "LinkedIn Ads",
    date: "Formato profesional",
    content: "Anuncios profesionales para LinkedIn. Diseño corporativo y elegante.",
    category: "Professional",
    icon: Linkedin,
    relatedIds: [1, 4],
    status: "completed",
    energy: 82,
    platform: "linkedin",
    sizes: ["1200x627", "1080x1080"],
    description: "Anuncios profesionales para LinkedIn",
    color: "from-[#3018ef] to-[#dd3a5a]"
  },
  {
    id: 6,
    title: "YouTube Ads",
    date: "Formato video",
    content: "Thumbnails y anuncios para YouTube. Formato horizontal optimizado.",
    category: "Video Platform",
    icon: Youtube,
    relatedIds: [3, 7],
    status: "completed",
    energy: 87,
    platform: "youtube",
    sizes: ["1280x720", "1920x1080"],
    description: "Thumbnails que destacan en YouTube",
    color: "from-[#dd3a5a] to-[#3018ef]"
  },
  {
    id: 7,
    title: "Display Banners",
    date: "Formato web",
    content: "Banners display para sitios web. Múltiples tamaños estándar.",
    category: "Web Display",
    icon: Monitor,
    relatedIds: [4, 6],
    status: "completed",
    energy: 80,
    platform: "display",
    sizes: ["728x90", "300x250", "320x50"],
    description: "Banners para sitios web",
    color: "from-[#3018ef] to-[#dd3a5a]"
  }
];

function RadialOrbitalTimeline({ timelineData }: RadialOrbitalTimelineProps) {
  const [expandedItems, setExpandedItems] = useState<Record<number, boolean>>({});
  const [rotationAngle, setRotationAngle] = useState<number>(0);
  const [autoRotate, setAutoRotate] = useState<boolean>(true);
  const [pulseEffect, setPulseEffect] = useState<Record<number, boolean>>({});
  const [centerOffset] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
  const [activeNodeId, setActiveNodeId] = useState<number | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const orbitRef = useRef<HTMLDivElement>(null);
  const nodeRefs = useRef<Record<number, HTMLDivElement | null>>({});
  const [, navigate] = useLocation();
  const { toast } = useToast();

  const handleContainerClick = (e: React.MouseEvent<HTMLDivElement>) => {
    // Si hay elementos expandidos, cualquier clic fuera de las cards los cierra
    if (Object.keys(expandedItems).length > 0) {
      const target = e.target as HTMLElement;
      // Verificar si el clic fue en una card o sus elementos hijos
      const isClickOnCard = target.closest('[data-card="true"]');
      const isClickOnNode = target.closest('[data-node="true"]');

      if (!isClickOnCard && !isClickOnNode) {
        setExpandedItems({});
        setActiveNodeId(null);
        setPulseEffect({});
        setAutoRotate(true);
      }
    }
  };

  const toggleItem = (id: number) => {
    setExpandedItems((prev) => {
      const newState = { ...prev };
      Object.keys(newState).forEach((key) => {
        if (parseInt(key) !== id) {
          newState[parseInt(key)] = false;
        }
      });

      newState[id] = !prev[id];

      if (!prev[id]) {
        setActiveNodeId(id);
        setAutoRotate(false);

        const relatedItems = getRelatedItems(id);
        const newPulseEffect: Record<number, boolean> = {};
        relatedItems.forEach((relId) => {
          newPulseEffect[relId] = true;
        });
        setPulseEffect(newPulseEffect);

        centerViewOnNode(id);
      } else {
        setActiveNodeId(null);
        setAutoRotate(true);
        setPulseEffect({});
      }

      return newState;
    });
  };

  const handleCreateAd = (platform: string) => {
    const platformName = timelineData.find(item => item.platform === platform)?.title || platform;

    // Cerrar cualquier modal/card expandida antes de navegar
    setExpandedItems({});
    setActiveNodeId(null);
    setPulseEffect({});
    setAutoRotate(true);

    // Mostrar toast de confirmación
    toast({
      title: "¡Plataforma seleccionada!",
      description: `Abriendo editor para ${platformName}...`,
    });

    // Navegar al editor de la plataforma seleccionada
    navigate(`/ad-creator/editor/${platform}`);
  };

  const handleFreeGeneration = () => {
    // Cerrar cualquier modal/card expandida
    setExpandedItems({});
    setActiveNodeId(null);
    setPulseEffect({});
    setAutoRotate(true);

    // Mostrar toast especial para generación libre
    toast({
      title: "🎲 ¡Generación Libre!",
      description: "Como Ideogram - directo al grano",
    });

    // Navegar a la página de generación libre
    navigate("/ad-creator/free-generation");
  };

  useEffect(() => {
    let rotationTimer: NodeJS.Timeout;

    if (autoRotate) {
      rotationTimer = setInterval(() => {
        setRotationAngle((prev) => {
          const newAngle = (prev + 0.3) % 360;
          return Number(newAngle.toFixed(3));
        });
      }, 50);
    }

    return () => {
      if (rotationTimer) {
        clearInterval(rotationTimer);
      }
    };
  }, [autoRotate]);

  const centerViewOnNode = (nodeId: number) => {
    const nodeIndex = timelineData.findIndex((item) => item.id === nodeId);
    const totalNodes = timelineData.length;
    const targetAngle = (nodeIndex / totalNodes) * 360;

    setRotationAngle(270 - targetAngle);
  };

  const calculateNodePosition = (index: number, total: number) => {
    const angle = ((index / total) * 360 + rotationAngle) % 360;
    const radius = 200;
    const radian = (angle * Math.PI) / 180;

    const x = radius * Math.cos(radian) + centerOffset.x;
    const y = radius * Math.sin(radian) + centerOffset.y;

    const zIndex = Math.round(100 + 50 * Math.cos(radian));
    const opacity = Math.max(0.4, Math.min(1, 0.4 + 0.6 * ((1 + Math.sin(radian)) / 2)));

    return { x, y, angle, zIndex, opacity };
  };

  const getRelatedItems = (itemId: number): number[] => {
    const currentItem = timelineData.find((item) => item.id === itemId);
    return currentItem ? currentItem.relatedIds : [];
  };

  const isRelatedToActive = (itemId: number): boolean => {
    if (!activeNodeId) return false;
    const relatedItems = getRelatedItems(activeNodeId);
    return relatedItems.includes(itemId);
  };



  return (
    <div
      className="w-full h-screen flex flex-col items-center justify-center bg-white overflow-hidden relative"
      ref={containerRef}
      onClick={handleContainerClick}
    >
      {/* Overlay cuando hay un item expandido - TEMPORALMENTE DESHABILITADO */}
      {false && activeNodeId && (
        <div
          className="absolute inset-0 bg-black/5 z-[100] transition-all duration-200 cursor-pointer"
          onClick={() => {
            setExpandedItems({});
            setActiveNodeId(null);
            setPulseEffect({});
            setAutoRotate(true);
          }}
        />
      )}

      {/* Back Button */}
      <div className="absolute top-8 left-8 z-50">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => navigate("/dashboard/ads-central")}
          className="text-gray-600 hover:text-gray-800 hover:bg-white/80 backdrop-blur-sm"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Volver a Ads Central
        </Button>
      </div>

      {/* Botón Flotante de Generación Libre */}
      <div className="absolute top-8 right-8 z-50">
        <Button
          onClick={handleFreeGeneration}
          className="bg-gradient-to-r from-[#dd3a5a] to-[#3018ef] hover:from-[#c73650] hover:to-[#2516d6] text-white px-6 py-3 rounded-full shadow-xl transition-all duration-300 transform hover:scale-105 group animate-pulse"
        >
          <Zap className="w-5 h-5 mr-2 group-hover:animate-pulse" />
          Generación Libre
          <Sparkles className="w-5 h-5 ml-2 group-hover:animate-bounce" />
        </Button>
      </div>

      {/* Header */}
      <div className="absolute top-8 left-1/2 transform -translate-x-1/2 z-50">
        <div className="text-center">
          <h1 className="text-4xl md:text-5xl font-black bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent mb-2">
            Elige tu plataforma
          </h1>
          <p className="text-gray-600 text-lg mb-4">
            Selecciona el tipo de anuncio que quieres crear
          </p>

          {/* Botón de Generación Libre en Header */}
          {!activeNodeId && (
            <div className="mb-4">
              <Button
                onClick={handleFreeGeneration}
                variant="outline"
                className="border-2 border-dashed border-[#dd3a5a] text-[#dd3a5a] hover:bg-[#dd3a5a] hover:text-white transition-all duration-300 px-6 py-2 rounded-full group bg-white/80 backdrop-blur-sm"
              >
                <Zap className="w-4 h-4 mr-2 group-hover:animate-pulse" />
                ¿Sin ideas? ¡Generación Libre!
                <Sparkles className="w-4 h-4 ml-2 group-hover:animate-bounce" />
              </Button>
            </div>
          )}

          {activeNodeId && (
            <div className="inline-flex items-center gap-2 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full shadow-lg">
              <div className="w-2 h-2 bg-[#3018ef] rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-gray-700">
                {timelineData.find(item => item.id === activeNodeId)?.title} seleccionado
              </span>
            </div>
          )}
        </div>
      </div>

      <div className="relative w-full max-w-4xl h-full flex items-center justify-center">
        <div
          className="absolute w-full h-full flex items-center justify-center"
          ref={orbitRef}
          style={{
            perspective: "1000px",
            transform: `translate(${centerOffset.x}px, ${centerOffset.y}px)`,
          }}
        >
          {/* Centro orbital */}
          <div className="absolute w-16 h-16 rounded-full bg-gradient-to-br from-[#3018ef] to-[#dd3a5a] flex items-center justify-center z-10 shadow-lg">
            <div className="absolute w-20 h-20 rounded-full border border-[#3018ef]/20 animate-pulse"></div>
            <div className="w-8 h-8 rounded-full bg-white shadow-inner"></div>
          </div>

          {/* Órbita */}
          <div
            className="absolute w-96 h-96 rounded-full border border-gray-200"
            style={{
              background: `conic-gradient(from 0deg, rgba(48, 24, 239, 0.03), rgba(221, 58, 90, 0.03), rgba(48, 24, 239, 0.03))`,
            }}
          ></div>

          {/* Nodos de plataformas */}
          {timelineData.map((item, index) => {
            const position = calculateNodePosition(index, timelineData.length);
            const isExpanded = expandedItems[item.id];
            const isRelated = isRelatedToActive(item.id);
            const isPulsing = pulseEffect[item.id];
            const Icon = item.icon;

            const nodeStyle = {
              transform: `translate(${position.x}px, ${position.y}px)`,
              zIndex: isExpanded ? 250 : position.zIndex,
              opacity: isExpanded ? 1 : position.opacity,
              transition: 'all 0.2s ease-out',
            };

            return (
              <div
                key={item.id}
                ref={(el) => (nodeRefs.current[item.id] = el)}
                data-node="true"
                className={`absolute cursor-pointer transition-all duration-200 ${
                  isExpanded ? 'z-[250]' : ''
                }`}
                style={nodeStyle}
                onClick={(e) => {
                  e.stopPropagation();
                  toggleItem(item.id);
                }}
              >
                <div
                  className={`absolute rounded-full -inset-1 ${
                    isPulsing ? "animate-pulse duration-2000" : ""
                  }`}
                  style={{
                    background: `radial-gradient(circle, rgba(48, 24, 239, 0.08) 0%, rgba(221, 58, 90, 0.05) 50%, rgba(48, 24, 239, 0) 70%)`,
                    width: `${item.energy * 0.3 + 35}px`,
                    height: `${item.energy * 0.3 + 35}px`,
                    left: `-${(item.energy * 0.3 + 35 - 40) / 2}px`,
                    top: `-${(item.energy * 0.3 + 35 - 40) / 2}px`,
                  }}
                ></div>

                <div
                  className={`
                  w-12 h-12 rounded-full flex items-center justify-center
                  ${
                    isExpanded
                      ? "bg-gradient-to-br from-[#3018ef] to-[#dd3a5a] text-white shadow-lg"
                      : isRelated
                      ? "bg-gradient-to-br from-[#3018ef]/60 to-[#dd3a5a]/60 text-white"
                      : "bg-white text-gray-700 shadow-md border border-gray-200"
                  }
                  transition-all duration-200 transform
                  ${isExpanded ? "scale-125 shadow-lg" : "hover:scale-105 hover:shadow-lg"}
                `}
                >
                  <Icon size={20} />
                </div>

                <div
                  className={`
                  absolute top-14 left-1/2 transform -translate-x-1/2 whitespace-nowrap
                  text-xs font-semibold tracking-wider
                  transition-all duration-200
                  ${isExpanded ? "text-gray-800 scale-110 font-bold" : "text-gray-600"}
                `}
                >
                  {item.title}
                </div>

                {isExpanded && (
                  <Card
                    data-card="true"
                    className="absolute top-20 left-1/2 -translate-x-1/2 w-72 max-w-[90vw] bg-white border-gray-200 shadow-xl z-[9999] transition-all duration-200"
                    style={{ pointerEvents: 'auto' }}
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                  >
                    <div className="absolute -top-3 left-1/2 -translate-x-1/2 w-px h-3 bg-gradient-to-b from-[#3018ef] to-[#dd3a5a]"></div>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-center">
                        <Badge className={`px-2 text-xs bg-gradient-to-r ${item.color} text-white border-none`}>
                          {item.category}
                        </Badge>
                        <div className="flex items-center gap-2">
                          <span className="text-xs font-mono text-gray-400">
                            {item.date}
                          </span>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 hover:bg-gray-100"
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleItem(item.id);
                            }}
                          >
                            <X size={12} className="text-gray-400" />
                          </Button>
                        </div>
                      </div>
                      <CardTitle className="text-sm mt-2 text-gray-800">
                        {item.title}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="text-xs text-gray-600">
                      <p className="mb-4">{item.content}</p>

                      <div className="mb-4">
                        <h4 className="text-xs uppercase tracking-wider font-medium text-gray-500 mb-2">
                          Tamaños disponibles
                        </h4>
                        <div className="flex flex-wrap gap-1">
                          {item.sizes.map((size) => (
                            <Badge key={size} variant="outline" className="text-xs border-gray-300 text-gray-600">
                              {size}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div className="mt-4 pt-3 border-t border-gray-200">
                        <div className="flex justify-between items-center text-xs mb-2">
                          <span className="flex items-center text-gray-600">
                            <Zap size={10} className="mr-1" />
                            Popularidad
                          </span>
                          <span className="font-mono text-gray-700">{item.energy}%</span>
                        </div>
                        <div className="w-full h-1 bg-gray-200 rounded-full overflow-hidden mb-4">
                          <div
                            className="h-full bg-gradient-to-r from-[#3018ef] to-[#dd3a5a]"
                            style={{ width: `${item.energy}%` }}
                          ></div>
                        </div>

                        <Button
                          onClick={(e) => {
                            e.stopPropagation();
                            e.preventDefault();
                            handleCreateAd(item.platform);
                          }}
                          className="w-full bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:opacity-90 text-white border-none shadow-lg transition-all duration-200"
                        >
                          Crear Anuncio
                          <ArrowRight size={14} className="ml-2" />
                        </Button>
                      </div>

                      {item.relatedIds.length > 0 && (
                        <div className="mt-4 pt-3 border-t border-gray-200">
                          <div className="flex items-center mb-2">
                            <Link size={10} className="text-gray-500 mr-1" />
                            <h4 className="text-xs uppercase tracking-wider font-medium text-gray-500">
                              Relacionados
                            </h4>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {item.relatedIds.map((relatedId) => {
                              const relatedItem = timelineData.find((i) => i.id === relatedId);
                              return (
                                <Button
                                  key={relatedId}
                                  variant="outline"
                                  size="sm"
                                  className="flex items-center h-6 px-2 py-0 text-xs border-gray-300 bg-transparent hover:bg-gray-50 text-gray-600 hover:text-gray-800 transition-all"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    toggleItem(relatedId);
                                  }}
                                >
                                  {relatedItem?.title}
                                  <ArrowRight size={8} className="ml-1 text-gray-400" />
                                </Button>
                              );
                            })}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Footer */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-50">
        <div className="text-center">
          <p className="text-gray-500 text-sm mb-4">
            {activeNodeId
              ? "Haz clic en 'Crear Anuncio' para continuar"
              : "Haz clic en cualquier plataforma para ver más detalles"
            }
          </p>

          {/* Botón de Generación Libre */}
          <div className="mb-4">
            <Button
              onClick={handleFreeGeneration}
              className="bg-gradient-to-r from-[#dd3a5a] to-[#3018ef] hover:from-[#c73650] hover:to-[#2516d6] text-white px-8 py-3 rounded-full shadow-xl transition-all duration-300 transform hover:scale-105 group"
            >
              <Zap className="w-5 h-5 mr-2 group-hover:animate-pulse" />
              Generación Libre
              <Sparkles className="w-5 h-5 ml-2 group-hover:animate-bounce" />
            </Button>
            <p className="text-xs text-gray-400 mt-2">
              Emma genera todo automáticamente - ¡No tienes que pensar en nada!
            </p>
          </div>

          <div className="flex items-center justify-center gap-4 text-xs text-gray-400">
            <span>🎨 Calidad profesional</span>
            <span>⚡ Generación rápida</span>
            <span>💾 Guardado automático</span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function AdTypeSelectorPage() {
  return (
    <DashboardLayout>
      <RadialOrbitalTimeline timelineData={adTypesData} />
    </DashboardLayout>
  );
}
