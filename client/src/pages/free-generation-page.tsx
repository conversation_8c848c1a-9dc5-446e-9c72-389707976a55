import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'wouter';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import {
  Zap,
  Sparkles,
  Upload,
  Wand2,
  Image as ImageIcon,
  Type,
  Palette,
  Target,
  Rocket,
  Brain,
  Star
} from 'lucide-react';

function FreeGenerationContent() {
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<File[]>([]);
  const [generatedResult, setGeneratedResult] = useState<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const [, navigate] = useLocation();

  const handleImageUpload = (files: FileList | null) => {
    if (files) {
      const newImages = Array.from(files).slice(0, 3); // Máximo 3 imágenes
      setUploadedImages(prev => [...prev, ...newImages].slice(0, 3));
    }
  };

  const removeImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index));
  };

  const handleFreeGeneration = async () => {
    if (!prompt.trim() && uploadedImages.length === 0) {
      toast({
        title: "¡Necesito algo!",
        description: "Escribe qué quieres o sube una imagen de tu producto",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);

    try {
      // Llamar al endpoint de generación libre con Ideogram
      const formData = new FormData();
      formData.append('prompt', prompt);
      formData.append('platform', 'instagram'); // Default platform
      formData.append('size', '1024x1024'); // Default size

      uploadedImages.forEach((image, index) => {
        formData.append(`image_${index}`, image);
      });

      const response = await fetch('/api/v1/ad-creator-agent/free-generation', {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        const result = await response.json();

        toast({
          title: "🎉 ¡Anuncio Generado con Ideogram!",
          description: "Emma creó tu anuncio automáticamente"
        });

        // Mostrar el resultado directamente en la página
        setGeneratedResult(result);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error en la generación');
      }
    } catch (error) {
      console.error('Error:', error);
      toast({
        title: "Error",
        description: error.message || "Hubo un problema. Inténtalo de nuevo.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const quickPrompts = [
    "Suplemento natural para dolor de cabeza",
    "App móvil innovadora para fitness",
    "Ropa deportiva premium",
    "Curso online de marketing digital",
    "Restaurante de comida saludable",
    "Servicio de consultoría empresarial"
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50">
      {/* Hero Section profesional estilo Emma */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="relative overflow-hidden rounded-2xl backdrop-blur-xl mb-8 mx-6 mt-6"
      >
        {/* Gradient background Emma */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#3018ef] via-[#4f46e5] to-[#dd3a5a] opacity-95"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>

        {/* Elementos flotantes decorativos */}
        <motion.div
          className="absolute right-8 top-8 w-20 h-20 bg-white/20 backdrop-blur-md rounded-2xl transform rotate-12 flex items-center justify-center shadow-2xl border border-white/30"
          animate={{ rotate: [12, 18, 12] }}
          transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
        >
          <Zap className="w-10 h-10 text-white" />
        </motion.div>

        <motion.div
          className="absolute left-8 bottom-8 w-16 h-16 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center shadow-xl border border-white/30"
          animate={{ y: [0, -10, 0] }}
          transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
        >
          <Sparkles className="w-8 h-8 text-white animate-pulse" />
        </motion.div>

        <div className="relative px-8 py-12 md:py-16 md:px-12">
          <div className="max-w-4xl mx-auto text-center">
            {/* Badge superior */}
            <motion.span
              className="inline-flex items-center bg-white/20 backdrop-blur-md text-white font-semibold px-6 py-3 rounded-full mb-6 border border-white/30 text-sm"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              whileHover={{ scale: 1.05 }}
            >
              <Rocket className="inline-block w-4 h-4 mr-2" />
              Tecnología Emma Pro • Nivel Agencias
            </motion.span>

            {/* Título principal */}
            <motion.h1
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
              className="text-4xl lg:text-6xl font-black mb-6 leading-tight"
            >
              <span className="text-white">Generador </span>
              <span className="bg-gradient-to-r from-[#dd3a5a] via-[#f472b6] to-[#fbbf24] bg-clip-text text-transparent">
                Premium
              </span>
            </motion.h1>

            {/* Subtítulo */}
            <motion.p
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5 }}
              className="text-xl text-white/90 max-w-3xl mx-auto mb-8 leading-relaxed"
            >
              La misma tecnología que usan las agencias top del mundo, ahora en tus manos.
              <br />
              <span className="text-white/70 text-lg">Resultados profesionales en segundos con la IA más avanzada del mercado</span>
            </motion.p>

            {/* Stats profesionales */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto"
            >
              {[
                { value: "Enterprise", label: "Tecnología", icon: Rocket, color: "text-[#dd3a5a]" },
                { value: "GPT-4", label: "Powered", icon: Brain, color: "text-[#fbbf24]" },
                { value: "Agency", label: "Level", icon: Star, color: "text-[#10b981]" },
                { value: "Premium", label: "Results", icon: Zap, color: "text-[#06b6d4]" }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.05, y: -2 }}
                  transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  className="bg-white/20 backdrop-blur-md rounded-xl p-4 border border-white/30 shadow-xl"
                >
                  <stat.icon className={`w-6 h-6 ${stat.color} mx-auto mb-2`} />
                  <div className="text-white font-bold text-lg">{stat.value}</div>
                  <div className="text-white/70 text-sm">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-6 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="space-y-8"
        >
          {/* Input Section con glassmorphism */}
          <Card className="bg-white/70 backdrop-blur-sm border border-gray-200/50 shadow-xl">
            <CardContent className="p-8">
              <div className="space-y-6">
                {/* Text Input */}
                <div>
                  <label className="text-gray-800 font-semibold mb-3 block text-lg">
                    ¿Qué quieres promocionar?
                  </label>
                  <Textarea
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    placeholder="Ej: Suplemento natural para dolor de cabeza, funciona en 15 minutos..."
                    className="bg-white/80 border-gray-300 text-gray-800 placeholder-gray-500 min-h-[120px] resize-none text-lg focus:ring-2 focus:ring-[#3018ef] focus:border-transparent"
                    maxLength={500}
                  />
                  <div className="text-right text-sm text-gray-500 mt-2">
                    {prompt.length}/500
                  </div>
                </div>

                {/* Image Upload */}
                <div>
                  <label className="text-gray-800 font-semibold mb-3 block text-lg">
                    O sube imágenes de tu producto (opcional)
                  </label>
                  <div className="flex items-center gap-4">
                    <Button
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                      className="border-[#3018ef] text-[#3018ef] hover:bg-[#3018ef] hover:text-white transition-all duration-300 px-6 py-3"
                    >
                      <Upload className="w-5 h-5 mr-2" />
                      Subir Imágenes
                    </Button>
                    <span className="text-gray-600 text-sm">
                      Máximo 3 imágenes
                    </span>
                  </div>
                  
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleImageUpload(e.target.files)}
                    className="hidden"
                  />

                  {/* Preview Images */}
                  {uploadedImages.length > 0 && (
                    <div className="flex gap-3 mt-4">
                      {uploadedImages.map((image, index) => (
                        <div key={index} className="relative">
                          <img
                            src={URL.createObjectURL(image)}
                            alt={`Preview ${index + 1}`}
                            className="w-24 h-24 object-cover rounded-xl border-2 border-gray-200 shadow-md"
                          />
                          <button
                            onClick={() => removeImage(index)}
                            className="absolute -top-2 -right-2 bg-[#dd3a5a] text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-[#c73650] transition-colors shadow-lg"
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Prompts */}
          <Card className="bg-white/70 backdrop-blur-sm border border-gray-200/50 shadow-xl">
            <CardContent className="p-8">
              <h3 className="text-gray-800 font-semibold mb-4 text-lg">💡 Ideas rápidas:</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {quickPrompts.map((quickPrompt, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    onClick={() => setPrompt(quickPrompt)}
                    className="text-left justify-start text-gray-700 hover:text-[#3018ef] hover:bg-[#3018ef]/10 text-sm h-auto py-3 px-4 border border-gray-200 hover:border-[#3018ef] transition-all duration-300 rounded-lg"
                  >
                    {quickPrompt}
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Generate Button */}
          <div className="text-center">
            <Button
              onClick={handleFreeGeneration}
              disabled={isGenerating || (!prompt.trim() && uploadedImages.length === 0)}
              className="bg-gradient-to-r from-[#dd3a5a] to-[#3018ef] hover:from-[#c73650] hover:to-[#2516d6] text-white px-16 py-6 text-xl font-bold rounded-full shadow-2xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              {isGenerating ? (
                <>
                  <Sparkles className="w-7 h-7 mr-4 animate-spin" />
                  Emma está creando tu anuncio...
                </>
              ) : (
                <>
                  <Wand2 className="w-7 h-7 mr-4" />
                  Generar Nivel Premium
                  <Zap className="w-7 h-7 ml-4" />
                </>
              )}
            </Button>

            {!isGenerating && (
              <p className="text-gray-600 text-base mt-4 max-w-md mx-auto">
                Tecnología enterprise que genera campañas de calidad profesional instantáneamente
              </p>
            )}
          </div>

          {/* Generated Result */}
          {generatedResult && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-8"
            >
              <Card className="bg-white/70 backdrop-blur-sm border border-gray-200/50 shadow-xl">
                <CardContent className="p-8">
                  <div className="text-center mb-6">
                    <h3 className="text-3xl font-bold text-gray-800 mb-3">
                      🚀 ¡Campaña Premium Lista!
                    </h3>
                    <p className="text-gray-600 text-lg">
                      Generado con tecnología Emma Enterprise - Calidad nivel agencias
                    </p>
                  </div>

                  {generatedResult.image_url && (
                    <div className="flex justify-center mb-6">
                      <img
                        src={generatedResult.image_url}
                        alt="Anuncio generado"
                        className="max-w-lg w-full rounded-xl shadow-2xl border-2 border-gray-200"
                      />
                    </div>
                  )}

                  <div className="text-center space-y-6">
                    <div className="text-sm text-gray-600 bg-gray-50 rounded-lg p-4">
                      <p className="mb-2"><strong>Prompt usado:</strong> {generatedResult.prompt_used}</p>
                      <p className="mb-2"><strong>Plataforma:</strong> {generatedResult.platform}</p>
                      <p><strong>Tamaño:</strong> {generatedResult.size}</p>
                    </div>

                    <div className="flex gap-4 justify-center">
                      <Button
                        onClick={() => {
                          if (generatedResult.image_url) {
                            window.open(generatedResult.image_url, '_blank');
                          }
                        }}
                        className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:opacity-90 text-white px-6 py-3"
                      >
                        <ImageIcon className="w-5 h-5 mr-2" />
                        Ver Resultado Premium
                      </Button>

                      <Button
                        onClick={() => {
                          setGeneratedResult(null);
                          setPrompt('');
                          setUploadedImages([]);
                        }}
                        variant="outline"
                        className="border-[#dd3a5a] text-[#dd3a5a] hover:bg-[#dd3a5a] hover:text-white px-6 py-3"
                      >
                        <Sparkles className="w-5 h-5 mr-2" />
                        Nueva Campaña
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Features Premium */}
          {!generatedResult && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-16">
              {[
                { icon: ImageIcon, title: "Enterprise Visual", desc: "Calidad nivel agencias", color: "text-[#dd3a5a]" },
                { icon: Type, title: "Copy Premium", desc: "Headlines que convierten", color: "text-[#3018ef]" },
                { icon: Palette, title: "Brand Design", desc: "Diseño profesional", color: "text-purple-600" },
                { icon: Target, title: "High Performance", desc: "ROI maximizado", color: "text-green-600" }
              ].map((feature, index) => (
                <div key={index} className="text-center p-4 bg-white/50 backdrop-blur-sm rounded-xl border border-gray-200/50 hover:shadow-lg transition-all duration-300">
                  <feature.icon className={`w-10 h-10 ${feature.color} mx-auto mb-3`} />
                  <h4 className="text-gray-800 font-semibold text-base mb-1">{feature.title}</h4>
                  <p className="text-gray-600 text-sm">{feature.desc}</p>
                </div>
              ))}
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
}

// Componente principal con DashboardLayout
export default function FreeGenerationPage() {
  return (
    <DashboardLayout pageTitle="Generación Libre">
      <FreeGenerationContent />
    </DashboardLayout>
  );
}
