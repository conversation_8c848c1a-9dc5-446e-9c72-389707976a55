import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'wouter';
import { 
  Zap, 
  Sparkles, 
  Upload, 
  ArrowLeft, 
  Wand2,
  Image as ImageIcon,
  Type,
  Palette,
  Target
} from 'lucide-react';

export default function FreeGenerationPage() {
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<File[]>([]);
  const [generatedResult, setGeneratedResult] = useState<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleImageUpload = (files: FileList | null) => {
    if (files) {
      const newImages = Array.from(files).slice(0, 3); // Máximo 3 imágenes
      setUploadedImages(prev => [...prev, ...newImages].slice(0, 3));
    }
  };

  const removeImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index));
  };

  const handleFreeGeneration = async () => {
    if (!prompt.trim() && uploadedImages.length === 0) {
      toast({
        title: "¡Necesito algo!",
        description: "Escribe qué quieres o sube una imagen de tu producto",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);

    try {
      // Llamar al endpoint de generación libre con Ideogram
      const formData = new FormData();
      formData.append('prompt', prompt);
      formData.append('platform', 'instagram'); // Default platform
      formData.append('size', '1024x1024'); // Default size

      uploadedImages.forEach((image, index) => {
        formData.append(`image_${index}`, image);
      });

      const response = await fetch('/api/v1/ad-creator-agent/free-generation', {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        const result = await response.json();

        toast({
          title: "🎉 ¡Anuncio Generado con Ideogram!",
          description: "Emma creó tu anuncio automáticamente"
        });

        // Mostrar el resultado directamente en la página
        setGeneratedResult(result);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error en la generación');
      }
    } catch (error) {
      console.error('Error:', error);
      toast({
        title: "Error",
        description: error.message || "Hubo un problema. Inténtalo de nuevo.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const quickPrompts = [
    "Suplemento natural para dolor de cabeza",
    "App móvil innovadora para fitness",
    "Ropa deportiva premium",
    "Curso online de marketing digital",
    "Restaurante de comida saludable",
    "Servicio de consultoría empresarial"
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <motion.header
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-4"
      >
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <Button
            variant="ghost"
            onClick={() => navigate('/dashboard/ads-central')}
            className="text-white hover:bg-white/10"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Volver
          </Button>
          
          <div className="text-center">
            <h1 className="text-2xl font-bold bg-gradient-to-r from-[#dd3a5a] to-[#3018ef] bg-clip-text text-transparent">
              Generación Libre
            </h1>
            <p className="text-gray-300 text-sm">Como Ideogram - Directo al grano</p>
          </div>
          
          <div className="w-20"></div>
        </div>
      </motion.header>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto p-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          {/* Hero Section */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center gap-2 mb-4">
              <Zap className="w-8 h-8 text-[#dd3a5a] animate-pulse" />
              <Sparkles className="w-8 h-8 text-[#3018ef] animate-bounce" />
            </div>
            <h2 className="text-4xl font-black text-white mb-2">
              ¡Dime qué quieres y YA!
            </h2>
            <p className="text-gray-300 text-lg">
              Sin pasos, sin complicaciones. Como Ideogram pero para anuncios.
            </p>
          </div>

          {/* Input Section */}
          <Card className="bg-black/40 backdrop-blur-sm border-purple-500/20 mb-6">
            <CardContent className="p-6">
              <div className="space-y-4">
                {/* Text Input */}
                <div>
                  <label className="text-white font-medium mb-2 block">
                    ¿Qué quieres promocionar?
                  </label>
                  <Textarea
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    placeholder="Ej: Suplemento natural para dolor de cabeza, funciona en 15 minutos..."
                    className="bg-white/10 border-purple-500/30 text-white placeholder-gray-400 min-h-[100px] resize-none"
                    maxLength={500}
                  />
                  <div className="text-right text-xs text-gray-400 mt-1">
                    {prompt.length}/500
                  </div>
                </div>

                {/* Image Upload */}
                <div>
                  <label className="text-white font-medium mb-2 block">
                    O sube imágenes de tu producto (opcional)
                  </label>
                  <div className="flex items-center gap-4">
                    <Button
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                      className="border-purple-500/30 text-white hover:bg-purple-500/20"
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      Subir Imágenes
                    </Button>
                    <span className="text-gray-400 text-sm">
                      Máximo 3 imágenes
                    </span>
                  </div>
                  
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleImageUpload(e.target.files)}
                    className="hidden"
                  />

                  {/* Preview Images */}
                  {uploadedImages.length > 0 && (
                    <div className="flex gap-2 mt-4">
                      {uploadedImages.map((image, index) => (
                        <div key={index} className="relative">
                          <img
                            src={URL.createObjectURL(image)}
                            alt={`Preview ${index + 1}`}
                            className="w-20 h-20 object-cover rounded-lg border border-purple-500/30"
                          />
                          <button
                            onClick={() => removeImage(index)}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs hover:bg-red-600"
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Prompts */}
          <Card className="bg-black/40 backdrop-blur-sm border-purple-500/20 mb-6">
            <CardContent className="p-6">
              <h3 className="text-white font-medium mb-3">Ideas rápidas:</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {quickPrompts.map((quickPrompt, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    onClick={() => setPrompt(quickPrompt)}
                    className="text-left justify-start text-gray-300 hover:text-white hover:bg-purple-500/20 text-sm h-auto py-2 px-3"
                  >
                    {quickPrompt}
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Generate Button */}
          <div className="text-center">
            <Button
              onClick={handleFreeGeneration}
              disabled={isGenerating || (!prompt.trim() && uploadedImages.length === 0)}
              className="bg-gradient-to-r from-[#dd3a5a] to-[#3018ef] hover:from-[#c73650] hover:to-[#2516d6] text-white px-12 py-4 text-lg font-bold rounded-full shadow-2xl transition-all duration-300 transform hover:scale-105"
            >
              {isGenerating ? (
                <>
                  <Sparkles className="w-6 h-6 mr-3 animate-spin" />
                  Emma está creando tu anuncio...
                </>
              ) : (
                <>
                  <Wand2 className="w-6 h-6 mr-3" />
                  ¡Generar Ahora!
                  <Zap className="w-6 h-6 ml-3" />
                </>
              )}
            </Button>
            
            {!isGenerating && (
              <p className="text-gray-400 text-sm mt-3">
                Emma analizará tu producto y creará el anuncio perfecto automáticamente
              </p>
            )}
          </div>

          {/* Generated Result */}
          {generatedResult && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-8"
            >
              <Card className="bg-black/40 backdrop-blur-sm border-purple-500/20">
                <CardContent className="p-6">
                  <div className="text-center mb-4">
                    <h3 className="text-2xl font-bold text-white mb-2">
                      🎉 ¡Tu anuncio está listo!
                    </h3>
                    <p className="text-gray-300">
                      Generado con Ideogram - Calidad profesional
                    </p>
                  </div>

                  {generatedResult.image_url && (
                    <div className="flex justify-center mb-4">
                      <img
                        src={generatedResult.image_url}
                        alt="Anuncio generado"
                        className="max-w-md w-full rounded-lg shadow-xl border border-purple-500/30"
                      />
                    </div>
                  )}

                  <div className="text-center space-y-4">
                    <div className="text-sm text-gray-400">
                      <p><strong>Prompt usado:</strong> {generatedResult.prompt_used}</p>
                      <p><strong>Plataforma:</strong> {generatedResult.platform}</p>
                      <p><strong>Tamaño:</strong> {generatedResult.size}</p>
                    </div>

                    <div className="flex gap-4 justify-center">
                      <Button
                        onClick={() => {
                          if (generatedResult.image_url) {
                            window.open(generatedResult.image_url, '_blank');
                          }
                        }}
                        className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:opacity-90 text-white"
                      >
                        <ImageIcon className="w-4 h-4 mr-2" />
                        Ver Imagen Completa
                      </Button>

                      <Button
                        onClick={() => {
                          setGeneratedResult(null);
                          setPrompt('');
                          setUploadedImages([]);
                        }}
                        variant="outline"
                        className="border-purple-500/30 text-white hover:bg-purple-500/20"
                      >
                        <Sparkles className="w-4 h-4 mr-2" />
                        Generar Otro
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Features */}
          {!generatedResult && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-12">
              {[
                { icon: ImageIcon, title: "Visual", desc: "Imágenes optimizadas" },
                { icon: Type, title: "Texto", desc: "Headlines persuasivos" },
                { icon: Palette, title: "Diseño", desc: "Colores perfectos" },
                { icon: Target, title: "Conversión", desc: "CTAs efectivos" }
              ].map((feature, index) => (
                <div key={index} className="text-center">
                  <feature.icon className="w-8 h-8 text-[#dd3a5a] mx-auto mb-2" />
                  <h4 className="text-white font-medium text-sm">{feature.title}</h4>
                  <p className="text-gray-400 text-xs">{feature.desc}</p>
                </div>
              ))}
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
}
