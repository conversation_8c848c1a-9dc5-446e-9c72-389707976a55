import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'wouter';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import {
  Zap,
  Sparkles,
  Upload,
  Wand2,
  Image as ImageIcon,
  Type,
  Palette,
  Target,
  Rocket,
  Brain,
  Star
} from 'lucide-react';

function FreeGenerationContent() {
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<File[]>([]);
  const [generatedResult, setGeneratedResult] = useState<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const [, navigate] = useLocation();

  const handleImageUpload = (files: FileList | null) => {
    if (files) {
      const newImages = Array.from(files).slice(0, 3); // Máximo 3 imágenes
      setUploadedImages(prev => [...prev, ...newImages].slice(0, 3));
    }
  };

  const removeImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index));
  };

  const handleFreeGeneration = async () => {
    if (!prompt.trim() && uploadedImages.length === 0) {
      toast({
        title: "¡Necesito algo!",
        description: "Escribe qué quieres o sube una imagen de tu producto",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);

    try {
      // Llamar al endpoint de generación libre con Ideogram
      const formData = new FormData();
      formData.append('prompt', prompt);
      formData.append('platform', 'instagram'); // Default platform
      formData.append('size', '1024x1024'); // Default size

      uploadedImages.forEach((image, index) => {
        formData.append(`image_${index}`, image);
      });

      const response = await fetch('/api/v1/ad-creator-agent/free-generation', {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        const result = await response.json();

        toast({
          title: "🎉 ¡Anuncio Generado con Ideogram!",
          description: "Emma creó tu anuncio automáticamente"
        });

        // Mostrar el resultado directamente en la página
        setGeneratedResult(result);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error en la generación');
      }
    } catch (error) {
      console.error('Error:', error);
      toast({
        title: "Error",
        description: error.message || "Hubo un problema. Inténtalo de nuevo.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const quickPrompts = [
    "Suplemento natural para dolor de cabeza",
    "App móvil innovadora para fitness",
    "Ropa deportiva premium",
    "Curso online de marketing digital",
    "Restaurante de comida saludable",
    "Servicio de consultoría empresarial"
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50">
      {/* Hero Section con glassmorphism */}
      <div className="relative overflow-hidden bg-gradient-to-br from-[#3018ef]/10 via-[#dd3a5a]/5 to-[#3018ef]/10 border-b border-gray-200/50">
        <div className="absolute inset-0 bg-white/40 backdrop-blur-sm"></div>
        <div className="relative max-w-6xl mx-auto px-6 py-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <div className="flex items-center justify-center gap-3 mb-6">
              <div className="p-3 rounded-full bg-gradient-to-r from-[#dd3a5a] to-[#3018ef] shadow-lg">
                <Zap className="w-8 h-8 text-white" />
              </div>
              <div className="p-3 rounded-full bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] shadow-lg animate-pulse">
                <Sparkles className="w-8 h-8 text-white" />
              </div>
            </div>

            <h1 className="text-5xl font-black bg-gradient-to-r from-[#dd3a5a] to-[#3018ef] bg-clip-text text-transparent mb-4">
              ¡Dime qué quieres y YA!
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
              Sin pasos, sin complicaciones. Como Ideogram pero para anuncios perfectos.
            </p>

            {/* Stats rápidas */}
            <div className="flex items-center justify-center gap-8 text-sm text-gray-500">
              <div className="flex items-center gap-2">
                <Rocket className="w-4 h-4 text-[#dd3a5a]" />
                <span>Generación en segundos</span>
              </div>
              <div className="flex items-center gap-2">
                <Brain className="w-4 h-4 text-[#3018ef]" />
                <span>IA de última generación</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="w-4 h-4 text-yellow-500" />
                <span>Calidad profesional</span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-6 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="space-y-8"
        >
          {/* Input Section con glassmorphism */}
          <Card className="bg-white/70 backdrop-blur-sm border border-gray-200/50 shadow-xl">
            <CardContent className="p-8">
              <div className="space-y-6">
                {/* Text Input */}
                <div>
                  <label className="text-gray-800 font-semibold mb-3 block text-lg">
                    ¿Qué quieres promocionar?
                  </label>
                  <Textarea
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    placeholder="Ej: Suplemento natural para dolor de cabeza, funciona en 15 minutos..."
                    className="bg-white/80 border-gray-300 text-gray-800 placeholder-gray-500 min-h-[120px] resize-none text-lg focus:ring-2 focus:ring-[#3018ef] focus:border-transparent"
                    maxLength={500}
                  />
                  <div className="text-right text-sm text-gray-500 mt-2">
                    {prompt.length}/500
                  </div>
                </div>

                {/* Image Upload */}
                <div>
                  <label className="text-gray-800 font-semibold mb-3 block text-lg">
                    O sube imágenes de tu producto (opcional)
                  </label>
                  <div className="flex items-center gap-4">
                    <Button
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                      className="border-[#3018ef] text-[#3018ef] hover:bg-[#3018ef] hover:text-white transition-all duration-300 px-6 py-3"
                    >
                      <Upload className="w-5 h-5 mr-2" />
                      Subir Imágenes
                    </Button>
                    <span className="text-gray-600 text-sm">
                      Máximo 3 imágenes
                    </span>
                  </div>
                  
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleImageUpload(e.target.files)}
                    className="hidden"
                  />

                  {/* Preview Images */}
                  {uploadedImages.length > 0 && (
                    <div className="flex gap-3 mt-4">
                      {uploadedImages.map((image, index) => (
                        <div key={index} className="relative">
                          <img
                            src={URL.createObjectURL(image)}
                            alt={`Preview ${index + 1}`}
                            className="w-24 h-24 object-cover rounded-xl border-2 border-gray-200 shadow-md"
                          />
                          <button
                            onClick={() => removeImage(index)}
                            className="absolute -top-2 -right-2 bg-[#dd3a5a] text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-[#c73650] transition-colors shadow-lg"
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Prompts */}
          <Card className="bg-white/70 backdrop-blur-sm border border-gray-200/50 shadow-xl">
            <CardContent className="p-8">
              <h3 className="text-gray-800 font-semibold mb-4 text-lg">💡 Ideas rápidas:</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {quickPrompts.map((quickPrompt, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    onClick={() => setPrompt(quickPrompt)}
                    className="text-left justify-start text-gray-700 hover:text-[#3018ef] hover:bg-[#3018ef]/10 text-sm h-auto py-3 px-4 border border-gray-200 hover:border-[#3018ef] transition-all duration-300 rounded-lg"
                  >
                    {quickPrompt}
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Generate Button */}
          <div className="text-center">
            <Button
              onClick={handleFreeGeneration}
              disabled={isGenerating || (!prompt.trim() && uploadedImages.length === 0)}
              className="bg-gradient-to-r from-[#dd3a5a] to-[#3018ef] hover:from-[#c73650] hover:to-[#2516d6] text-white px-16 py-6 text-xl font-bold rounded-full shadow-2xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              {isGenerating ? (
                <>
                  <Sparkles className="w-7 h-7 mr-4 animate-spin" />
                  Emma está creando tu anuncio...
                </>
              ) : (
                <>
                  <Wand2 className="w-7 h-7 mr-4" />
                  ¡Generar Ahora!
                  <Zap className="w-7 h-7 ml-4" />
                </>
              )}
            </Button>

            {!isGenerating && (
              <p className="text-gray-600 text-base mt-4 max-w-md mx-auto">
                Emma analizará tu producto y creará el anuncio perfecto automáticamente
              </p>
            )}
          </div>

          {/* Generated Result */}
          {generatedResult && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-8"
            >
              <Card className="bg-black/40 backdrop-blur-sm border-purple-500/20">
                <CardContent className="p-6">
                  <div className="text-center mb-4">
                    <h3 className="text-2xl font-bold text-white mb-2">
                      🎉 ¡Tu anuncio está listo!
                    </h3>
                    <p className="text-gray-300">
                      Generado con Ideogram - Calidad profesional
                    </p>
                  </div>

                  {generatedResult.image_url && (
                    <div className="flex justify-center mb-4">
                      <img
                        src={generatedResult.image_url}
                        alt="Anuncio generado"
                        className="max-w-md w-full rounded-lg shadow-xl border border-purple-500/30"
                      />
                    </div>
                  )}

                  <div className="text-center space-y-4">
                    <div className="text-sm text-gray-400">
                      <p><strong>Prompt usado:</strong> {generatedResult.prompt_used}</p>
                      <p><strong>Plataforma:</strong> {generatedResult.platform}</p>
                      <p><strong>Tamaño:</strong> {generatedResult.size}</p>
                    </div>

                    <div className="flex gap-4 justify-center">
                      <Button
                        onClick={() => {
                          if (generatedResult.image_url) {
                            window.open(generatedResult.image_url, '_blank');
                          }
                        }}
                        className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:opacity-90 text-white"
                      >
                        <ImageIcon className="w-4 h-4 mr-2" />
                        Ver Imagen Completa
                      </Button>

                      <Button
                        onClick={() => {
                          setGeneratedResult(null);
                          setPrompt('');
                          setUploadedImages([]);
                        }}
                        variant="outline"
                        className="border-purple-500/30 text-white hover:bg-purple-500/20"
                      >
                        <Sparkles className="w-4 h-4 mr-2" />
                        Generar Otro
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Features */}
          {!generatedResult && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-12">
              {[
                { icon: ImageIcon, title: "Visual", desc: "Imágenes optimizadas" },
                { icon: Type, title: "Texto", desc: "Headlines persuasivos" },
                { icon: Palette, title: "Diseño", desc: "Colores perfectos" },
                { icon: Target, title: "Conversión", desc: "CTAs efectivos" }
              ].map((feature, index) => (
                <div key={index} className="text-center">
                  <feature.icon className="w-8 h-8 text-[#dd3a5a] mx-auto mb-2" />
                  <h4 className="text-white font-medium text-sm">{feature.title}</h4>
                  <p className="text-gray-400 text-xs">{feature.desc}</p>
                </div>
              ))}
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
}
